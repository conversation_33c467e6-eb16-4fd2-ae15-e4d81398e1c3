#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Analýza CSV sloupců vs. po<PERSON>et otázek
"""

import pandas as pd
import json

def analyze_csv_vs_questions():
    """Porovnání CSV sloupců s otázkami"""
    
    print("🔍 Analýza CSV sloupců vs. otázky")
    print("=" * 60)
    
    # Načtení CSV (se správným oddělovačem)
    csv_file = "data/dotazniky.urad.online/548754/responses.csv"
    df = pd.read_csv(csv_file, sep=';', nrows=1)
    
    print(f"📊 CSV obsahuje: {len(df.columns)} sloupců")
    print(f"🔍 Prvních 20 sloupců:")
    for i, col in enumerate(df.columns[:20], 1):
        print(f"   {i:2d}. {col}")
    
    if len(df.columns) > 20:
        print(f"   ... a dal<PERSON><PERSON>ch {len(df.columns) - 20} sloupců")
    
    # Analýza typů sloupců
    print(f"\n📋 Analýza typů sloupců:")
    
    # Systémové sloupce (metadata)
    system_columns = []
    question_columns = []
    
    for col in df.columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ['id', 'token', 'submitdate', 'lastpage', 'startlanguage', 'seed', 'startdate', 'datestamp']):
            system_columns.append(col)
        else:
            question_columns.append(col)
    
    print(f"   🔧 Systémové sloupce: {len(system_columns)}")
    for col in system_columns:
        print(f"      - {col}")
    
    print(f"   ❓ Sloupce otázek: {len(question_columns)}")
    print(f"   🔍 Prvních 10 sloupců otázek:")
    for col in question_columns[:10]:
        print(f"      - {col}")
    
    # Porovnání s JSON strukturou
    print(f"\n🔗 Porovnání s JSON strukturou:")
    
    json_file = "data/dotazniky.urad.online/548754/structure.json"
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Počítání otázek v JSON
    total_items = 0
    main_questions = 0
    subquestions = 0
    
    for group in data.get('groups', []):
        questions = group.get('questions', [])
        total_items += len(questions)
        
        for q in questions:
            title = q.get('title', '')
            if title.startswith('SQ') or '[SQ' in title:
                subquestions += 1
            else:
                main_questions += 1
    
    print(f"   📋 JSON celkem položek: {total_items}")
    print(f"   ❓ JSON hlavní otázky: {main_questions}")
    print(f"   📝 JSON subotázky: {subquestions}")
    
    print(f"\n🎯 Porovnání:")
    print(f"   📊 CSV sloupce otázek: {len(question_columns)}")
    print(f"   ❓ JSON hlavní otázky: {main_questions}")
    print(f"   🎯 LimeSurvey říká: 59 otázek")
    
    print(f"\n🤔 Hypotézy:")
    print(f"   1. LimeSurvey počítá pouze hlavní otázky: {main_questions} vs 59")
    print(f"   2. LimeSurvey počítá CSV sloupce: {len(question_columns)} vs 59")
    print(f"   3. LimeSurvey počítá něco jiného...")
    
    # Detailní analýza rozdílu
    if len(question_columns) == 59:
        print(f"\n🎉 BINGO! CSV sloupce otázek = LimeSurvey počet otázek!")
        print(f"   📊 CSV má přesně 59 sloupců otázek")
        print(f"   🎯 To odpovídá LimeSurvey počtu 59 otázek")
        print(f"   💡 LimeSurvey počítá sloupce v CSV, ne položky v JSON!")
    
    return len(question_columns), main_questions

if __name__ == "__main__":
    csv_count, json_count = analyze_csv_vs_questions()
    
    print("\n" + "=" * 60)
    if csv_count == 59:
        print("🎉 Problém vyřešen! LimeSurvey počítá CSV sloupce!")
    else:
        print("🤔 Stále hledáme správné vysvětlení...")
    print("=" * 60)
