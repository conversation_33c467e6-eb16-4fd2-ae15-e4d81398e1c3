#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test pro stažení LSS souboru přes LimeSurvey API
Testuje server1 (.env) a průzkum 548754
"""

import os
import sys
import base64
import logging
from pathlib import Path

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from limesurvey_client import LimeSurveyClient

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_lss_export():
    """Test stažení LSS souboru pro průzkum 548754"""
    
    print("🔍 Test stažení LSS souboru")
    print("=" * 50)
    
    # Parametry testu
    survey_id = "548754"
    server_key = "server1"  # Z .env
    
    try:
        # Inicializace klienta (používá config z .env)
        print(f"🌐 Inicializuji klienta pro server: {server_key}")
        client = LimeSurveyClient()
        
        # Test session key
        print("🔑 Testuji session key...")
        session_key = client.get_session_key()
        if not session_key:
            print("❌ Nepodařilo se získat session key")
            return False
        print(f"✅ Session key získán: {session_key[:10]}...")
        
        # Test správného API volání podle seznamu funkcí
        test_functions = [
            ("export_survey", []),  # Bez parametru formátu - podle tvého kódu
        ]
        
        for func_name, extra_params in test_functions:
            print(f"\n📤 Testuji {func_name} s parametry: {extra_params}")
            
            # Sestavení parametrů
            params = [session_key, survey_id] + extra_params
            
            try:
                # API volání
                result = client._make_request(func_name, params)
                
                if result:
                    print(f"✅ {func_name} úspěšné!")
                    
                    # Pokud je výsledek string, zkusíme ho dekódovat jako base64
                    if isinstance(result, str):
                        try:
                            decoded = base64.b64decode(result)
                            content = decoded.decode('utf-8')
                            
                            # Kontrola, jestli je to XML
                            if content.strip().startswith('<?xml'):
                                print(f"🎯 Nalezen XML obsah! Délka: {len(content)} znaků")
                                
                                # Uložení do testovacího souboru
                                test_file = f"test_lss_{func_name}_{survey_id}.lss"
                                with open(test_file, 'w', encoding='utf-8') as f:
                                    f.write(content)
                                
                                print(f"💾 LSS soubor uložen: {test_file}")
                                
                                # Základní validace XML
                                if '<survey' in content and '</survey>' in content:
                                    print("✅ XML obsahuje survey elementy")
                                    return True
                                else:
                                    print("⚠️ XML neobsahuje očekávané survey elementy")
                            else:
                                print(f"⚠️ Obsah není XML: {content[:100]}...")
                                
                        except Exception as e:
                            print(f"⚠️ Chyba při dekódování base64: {e}")
                            print(f"Raw result: {str(result)[:200]}...")
                    else:
                        print(f"⚠️ Neočekávaný typ výsledku: {type(result)}")
                        print(f"Result: {str(result)[:200]}...")
                        
                else:
                    print(f"❌ {func_name} vrátil prázdný výsledek")
                    
            except Exception as e:
                print(f"❌ Chyba při volání {func_name}: {e}")
        
        print("\n" + "=" * 50)
        print("❌ Žádné API volání nevrátilo platný LSS soubor")
        return False
        
    except Exception as e:
        print(f"❌ Kritická chyba: {e}")
        return False

def test_existing_api_calls():
    """Test existujících API volání pro porovnání"""
    
    print("\n🔍 Test existujících API volání")
    print("=" * 50)
    
    survey_id = "548754"
    server_key = "server1"
    
    try:
        client = LimeSurveyClient()
        
        # Test list_surveys
        print("📋 Testuji list_surveys...")
        surveys = client._make_request("list_surveys", [client.get_session_key()])
        if surveys:
            print(f"✅ list_surveys úspěšné - {len(surveys)} průzkumů")
        else:
            print("❌ list_surveys selhalo")
        
        # Test list_groups
        print("📁 Testuji list_groups...")
        groups = client._make_request("list_groups", [client.get_session_key(), survey_id])
        if groups:
            print(f"✅ list_groups úspěšné - {len(groups)} skupin")
        else:
            print("❌ list_groups selhalo")
            
        # Test export_responses (pro porovnání)
        print("📊 Testuji export_responses...")
        responses = client._make_request("export_responses", [
            client.get_session_key(),
            survey_id,
            'csv',
            None,
            'all',
            'en',
            True
        ])
        if responses:
            print("✅ export_responses úspěšné")
        else:
            print("❌ export_responses selhalo")
            
    except Exception as e:
        print(f"❌ Chyba při testování existujících API: {e}")

if __name__ == "__main__":
    print("🚀 Spouštím test LSS exportu")
    print("📋 Server: server1 (.env)")
    print("📊 Průzkum: 548754")
    print("=" * 50)
    
    # Test LSS exportu
    success = test_lss_export()
    
    # Test existujících API volání
    test_existing_api_calls()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test dokončen úspěšně!")
    else:
        print("⚠️ Test dokončen - LSS export se nepodařil")
    print("=" * 50)
