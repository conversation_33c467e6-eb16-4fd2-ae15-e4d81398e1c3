#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test celého workflow od robustního parseru až po přípravu dat pro grafy
"""

import os
import sys
import json
import logging
import pandas as pd
from pathlib import Path

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_full_workflow():
    """Test celého workflow pro průzkum 548754"""
    
    print("🚀 Test celého workflow")
    print("📊 Průzkum: 548754")
    print("=" * 60)
    
    survey_id = "548754"
    server_key = "dotazniky.urad.online"
    data_dir = f"data/{server_key}/{survey_id}"
    
    # Kontrola existence základních soubor<PERSON>
    required_files = [
        f"{data_dir}/structure.lss",
        f"{data_dir}/responses.csv"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ Chybí základní soubory:")
        for f in missing_files:
            print(f"   - {f}")
        print("💡 Spusťte nejprve Menu 2 a 3 v hlavní aplikaci")
        return False
    
    try:
        # Krok 1: Test robustního JSON parseru
        print("\n📋 Krok 1: Robustní JSON parser")
        print("-" * 40)
        
        from robust_json_parser import RobustJSONParser
        
        # Zkopírujeme .lss jako .json pro parser
        lss_file = f"{data_dir}/structure.lss"
        json_file = f"{data_dir}/structure.json"
        
        if not os.path.exists(json_file):
            import shutil
            shutil.copy2(lss_file, json_file)
            print(f"📄 Zkopírován {lss_file} -> {json_file}")
        
        parser = RobustJSONParser()
        survey = parser.parse_json_file(json_file)
        
        if not survey:
            print("❌ Robustní parser selhal")
            return False
        
        print(f"✅ Parser úspěšný: {len(survey.groups)} skupin, {len(survey.questions)} otázek")
        
        # Export vylepšené struktury
        enhanced_file = f"{data_dir}/structure_enhanced.json"
        if parser.export_to_json(survey, enhanced_file):
            print(f"💾 Vylepšená struktura: {enhanced_file}")
        
        # Krok 2: Test mapování CSV a JSON
        print("\n🔗 Krok 2: Mapování CSV a JSON")
        print("-" * 40)
        
        # Načtení CSV dat
        csv_file = f"{data_dir}/responses.csv"
        if not os.path.exists(csv_file):
            print(f"❌ CSV soubor neexistuje: {csv_file}")
            return False
        
        # Načtení prvních řádků pro analýzu (CSV používá středník jako oddělovač)
        df = pd.read_csv(csv_file, nrows=5, sep=';')
        print(f"📊 CSV načten: {len(df.columns)} sloupců, ukázka {len(df)} řádků")
        print(f"🔍 Ukázkové sloupce: {list(df.columns[:10])}")
        
        # Krok 3: Kontrola mapování
        print("\n📋 Krok 3: Kontrola mapování")
        print("-" * 40)
        
        mapping_file = f"{data_dir}/question_mapping.csv"
        if os.path.exists(mapping_file):
            mapping_df = pd.read_csv(mapping_file)
            print(f"✅ Mapování existuje: {len(mapping_df)} záznamů")
            print(f"🔍 Sloupce mapování: {list(mapping_df.columns)}")
        else:
            print(f"⚠️ Mapování neexistuje: {mapping_file}")
            print("💡 Bude potřeba vytvořit v Menu 4")
        
        # Krok 4: Kontrola long formátu
        print("\n📈 Krok 4: Kontrola long formátu")
        print("-" * 40)
        
        long_file = f"{data_dir}/responses_long.csv"
        if os.path.exists(long_file):
            long_df = pd.read_csv(long_file, nrows=10)
            print(f"✅ Long formát existuje: {len(long_df.columns)} sloupců")
            print(f"🔍 Sloupce long: {list(long_df.columns)}")
        else:
            print(f"⚠️ Long formát neexistuje: {long_file}")
            print("💡 Bude potřeba vytvořit v Menu 5")
        
        # Krok 5: Kontrola dat pro grafy
        print("\n📊 Krok 5: Kontrola dat pro grafy")
        print("-" * 40)
        
        chart_data_file = f"{data_dir}/chart_data.json"
        if os.path.exists(chart_data_file):
            with open(chart_data_file, 'r', encoding='utf-8') as f:
                chart_data = json.load(f)
            print(f"✅ Data pro grafy existují: {len(chart_data)} záznamů")
        else:
            print(f"⚠️ Data pro grafy neexistují: {chart_data_file}")
            print("💡 Bude potřeba vytvořit v Menu 6")
        
        # Krok 6: Kontrola překladů
        print("\n🌍 Krok 6: Kontrola překladů")
        print("-" * 40)
        
        translations_file = f"{data_dir}/translations_cs-CZ.json"
        if os.path.exists(translations_file):
            with open(translations_file, 'r', encoding='utf-8') as f:
                translations = json.load(f)
            print(f"✅ Překlady existují: {len(translations)} kategorií")
            
            # Analýza překladů
            for category, items in translations.items():
                if isinstance(items, dict):
                    print(f"   📝 {category}: {len(items)} položek")
        else:
            print(f"⚠️ Překlady neexistují: {translations_file}")
            print("💡 Bude potřeba vytvořit v Menu 10")
        
        # Krok 7: Kontrola filtru soukromí
        print("\n🔒 Krok 7: Kontrola filtru soukromí")
        print("-" * 40)
        
        privacy_file = f"{data_dir}/privacy_filter.json"
        if os.path.exists(privacy_file):
            with open(privacy_file, 'r', encoding='utf-8') as f:
                privacy_filter = json.load(f)
            print(f"✅ Filtr soukromí existuje")
            
            blocked_columns = privacy_filter.get('blocked_columns', [])
            allowed_columns = privacy_filter.get('allowed_columns', [])
            
            print(f"   🚫 Blokované sloupce: {len(blocked_columns)}")
            print(f"   ✅ Povolené sloupce: {len(allowed_columns)}")
            
            if blocked_columns:
                print(f"   🔍 Ukázka blokovaných: {blocked_columns[:5]}")
        else:
            print(f"⚠️ Filtr soukromí neexistuje: {privacy_file}")
            print("💡 Bude potřeba vytvořit v Menu 16")
        
        print("\n" + "=" * 60)
        print("📋 Shrnutí workflow:")
        print("✅ Robustní JSON parser - funguje")
        print("📊 CSV data - k dispozici")
        print("🔗 Mapování - " + ("✅ existuje" if os.path.exists(mapping_file) else "⚠️ chybí"))
        print("📈 Long formát - " + ("✅ existuje" if os.path.exists(long_file) else "⚠️ chybí"))
        print("📊 Data pro grafy - " + ("✅ existují" if os.path.exists(chart_data_file) else "⚠️ chybí"))
        print("🌍 Překlady - " + ("✅ existují" if os.path.exists(translations_file) else "⚠️ chybí"))
        print("🔒 Filtr soukromí - " + ("✅ existuje" if os.path.exists(privacy_file) else "⚠️ chybí"))
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování workflow: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_workflow()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Test workflow dokončen!")
        print("💡 Pokračujte s chybějícími kroky v hlavní aplikaci")
    else:
        print("❌ Test workflow selhal")
    print("=" * 60)
