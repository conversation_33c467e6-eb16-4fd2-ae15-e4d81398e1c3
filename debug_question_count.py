#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug počtu otázek - porovnání s LimeSurvey
"""

import os
import sys
import json
import logging

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from robust_json_parser import RobustJSONParser

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def analyze_original_structure():
    """Analýza původní JSON struktury"""
    
    print("🔍 Analýza původní JSON struktury")
    print("=" * 60)
    
    json_file = "data/dotazniky.urad.online/548754/structure.json"
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    groups = data.get('groups', [])
    print(f"📁 Počet skupin: {len(groups)}")
    
    total_questions = 0
    main_questions = 0
    subquestions = 0
    
    for i, group in enumerate(groups, 1):
        group_name = group.get('group_name', f'Skupina {i}')
        questions = group.get('questions', [])
        
        group_main = 0
        group_sub = 0
        
        print(f"\n📋 {i}. {group_name}")
        print(f"   Celkem položek: {len(questions)}")
        
        # Analýza otázek ve skupině
        for q in questions:
            qid = q.get('qid')
            title = q.get('title', '')
            qtype = q.get('type', '')
            question_text = q.get('question', '')[:50] + "..." if len(q.get('question', '')) > 50 else q.get('question', '')
            
            # Určení, zda je to hlavní otázka nebo subotázka
            is_subquestion = (
                title.startswith('SQ') or 
                (title.startswith('G') and '[SQ' in title) or
                ('SQ' in title and len(title) <= 10)
            )
            
            if is_subquestion:
                group_sub += 1
                subquestions += 1
                print(f"      📝 SUB: [{qid}] {title} ({qtype}) - {question_text}")
            else:
                group_main += 1
                main_questions += 1
                print(f"      ❓ MAIN: [{qid}] {title} ({qtype}) - {question_text}")
        
        total_questions += len(questions)
        print(f"   📊 Hlavní otázky: {group_main}, Subotázky: {group_sub}")
    
    print("\n" + "=" * 60)
    print(f"📊 CELKOVÉ STATISTIKY:")
    print(f"   📁 Skupiny: {len(groups)}")
    print(f"   📋 Celkem položek: {total_questions}")
    print(f"   ❓ Hlavní otázky: {main_questions}")
    print(f"   📝 Subotázky: {subquestions}")
    print(f"   🎯 LimeSurvey říká: 59 otázek")
    print(f"   🤔 Rozdíl: {main_questions - 59}")

def analyze_enhanced_structure():
    """Analýza vylepšené struktury z robustního parseru"""
    
    print("\n🔍 Analýza vylepšené struktury (robustní parser)")
    print("=" * 60)
    
    json_file = "data/dotazniky.urad.online/548754/structure.json"
    
    parser = RobustJSONParser()
    survey = parser.parse_json_file(json_file)
    
    if not survey:
        print("❌ Nepodařilo se načíst survey")
        return
    
    print(f"📁 Počet skupin: {len(survey.groups)}")
    
    total_main = 0
    total_sub = 0
    
    for i, group in enumerate(survey.groups, 1):
        print(f"\n📋 {i}. {group.group_name}")
        print(f"   Hlavní otázky: {len(group.questions)}")
        
        group_sub_count = 0
        for question in group.questions:
            group_sub_count += len(question.subquestions)
            
            if question.subquestions:
                print(f"      ❓ [{question.qid}] {question.title} ({question.type}) - {len(question.subquestions)} subotázek")
            else:
                print(f"      ❓ [{question.qid}] {question.title} ({question.type}) - bez subotázek")
        
        total_main += len(group.questions)
        total_sub += group_sub_count
        print(f"   📊 Subotázky: {group_sub_count}")
    
    print("\n" + "=" * 60)
    print(f"📊 VYLEPŠENÉ STATISTIKY:")
    print(f"   📁 Skupiny: {len(survey.groups)}")
    print(f"   ❓ Hlavní otázky: {total_main}")
    print(f"   📝 Subotázky: {total_sub}")
    print(f"   🎯 LimeSurvey říká: 59 otázek")
    print(f"   🤔 Rozdíl: {total_main - 59}")

def compare_with_mapping():
    """Porovnání s mapovacím souborem"""
    
    print("\n🔍 Porovnání s mapovacím souborem")
    print("=" * 60)
    
    mapping_file = "data/dotazniky.urad.online/548754/question_mapping.csv"
    
    if not os.path.exists(mapping_file):
        print("❌ Mapovací soubor neexistuje")
        return
    
    import pandas as pd
    
    df = pd.read_csv(mapping_file)
    print(f"📋 Mapovací soubor obsahuje: {len(df)} záznamů")
    
    # Analýza hlavních otázek v mapování
    main_questions = df[df['is_main_question'] == True]
    print(f"❓ Hlavní otázky v mapování: {len(main_questions)}")
    
    # Analýza typů otázek
    type_counts = df['question_type'].value_counts()
    print(f"📊 Typy otázek v mapování:")
    for qtype, count in type_counts.items():
        print(f"   {qtype}: {count}")
    
    print(f"🎯 LimeSurvey říká: 59 otázek")
    print(f"🤔 Rozdíl s mapováním: {len(main_questions) - 59}")

if __name__ == "__main__":
    print("🚀 Debug počtu otázek - průzkum 548754")
    print("🎯 LimeSurvey říká: 59 otázek, 12 skupin")
    print("🤔 Robustní parser našel: 90 otázek")
    print("=" * 60)
    
    # Analýza původní struktury
    analyze_original_structure()
    
    # Analýza vylepšené struktury
    analyze_enhanced_structure()
    
    # Porovnání s mapováním
    compare_with_mapping()
    
    print("\n" + "=" * 60)
    print("🎯 Závěr: Potřebujeme najít, kde je rozdíl!")
    print("=" * 60)
