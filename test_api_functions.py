#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test dostupných API funkcí na LimeSurvey serveru
"""

import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

def test_api_function(api_url, session_key, function_name, params):
    """Test jedné API funkce"""
    
    data = {
        'method': function_name,
        'params': params,
        'id': 1,
        'jsonrpc': '2.0'
    }
    
    try:
        response = requests.post(api_url, json=data)
        
        if response.status_code == 200:
            try:
                result = response.json()
                if 'result' in result and result['result'] is not None:
                    return True, f"✅ {function_name} - funguje"
                elif 'error' in result:
                    return False, f"❌ {function_name} - chyba: {result['error']}"
                else:
                    return False, f"⚠️ {function_name} - prázdný výsledek"
            except json.JSONDecodeError:
                return False, f"❌ {function_name} - nep<PERSON><PERSON><PERSON>SO<PERSON>"
        else:
            return False, f"❌ {function_name} - HTTP {response.status_code}"
            
    except Exception as e:
        return False, f"❌ {function_name} - výjimka: {e}"

def main():
    print("🔍 Test dostupných API funkcí")
    print("=" * 50)
    
    # Konfigurace
    api_url = os.getenv('LIMESURVEY_API_URL')
    username = os.getenv('LIMESURVEY_USERNAME')
    password = os.getenv('LIMESURVEY_PASSWORD')
    survey_id = 548754
    
    if not all([api_url, username, password]):
        print("❌ Chybí konfigurace v .env")
        return
    
    # Získání session key
    login_data = {
        'method': 'get_session_key',
        'params': {
            'username': username,
            'password': password
        },
        'id': 1,
        'jsonrpc': '2.0'
    }
    
    response = requests.post(api_url, json=login_data)
    session_key = response.json()['result']
    print(f"✅ Session key: {session_key[:10]}...")
    
    # Seznam funkcí k testování
    test_functions = [
        # Základní funkce (víme, že fungují)
        ("list_surveys", {"sSessionKey": session_key}),
        ("list_groups", {"sSessionKey": session_key, "iSurveyID": survey_id}),
        ("list_questions", {"sSessionKey": session_key, "iSurveyID": survey_id}),
        ("get_survey_properties", {"sSessionKey": session_key, "iSurveyID": survey_id}),
        
        # Export funkce (testujeme)
        ("export_survey", {"sSessionKey": session_key, "iSurveyID": survey_id}),
        ("export_responses", {"sSessionKey": session_key, "iSurveyID": survey_id, "sDocumentType": "csv"}),
        
        # Možné alternativy
        ("get_fieldmap", {"sSessionKey": session_key, "iSurveyID": survey_id}),
        ("get_summary", {"sSessionKey": session_key, "iSurveyID": survey_id}),
        
        # Neexistující funkce (pro porovnání)
        ("export_structure", {"sSessionKey": session_key, "iSurveyID": survey_id}),
        ("get_survey_structure", {"sSessionKey": session_key, "iSurveyID": survey_id}),
    ]
    
    print(f"\n📋 Testování {len(test_functions)} API funkcí:")
    print("-" * 50)
    
    working_functions = []
    
    for func_name, params in test_functions:
        success, message = test_api_function(api_url, session_key, func_name, params)
        print(message)
        
        if success:
            working_functions.append(func_name)
    
    print("\n" + "=" * 50)
    print(f"📊 Výsledky:")
    print(f"✅ Fungující funkce ({len(working_functions)}):")
    for func in working_functions:
        print(f"   - {func}")
    
    print(f"\n❌ Nefungující funkce ({len(test_functions) - len(working_functions)}):")
    for func_name, _ in test_functions:
        if func_name not in working_functions:
            print(f"   - {func_name}")
    
    # Uvolnění session key
    logout_data = {
        'method': 'release_session_key',
        'params': {'sSessionKey': session_key},
        'id': 1,
        'jsonrpc': '2.0'
    }
    requests.post(api_url, json=logout_data)
    print(f"\n🔓 Session key uvolněn")

if __name__ == "__main__":
    main()
