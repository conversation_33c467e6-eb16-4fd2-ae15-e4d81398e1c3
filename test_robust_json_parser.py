#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test robustního JSON parseru
"""

import os
import sys
import json
import logging

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from robust_json_parser import RobustJSONParser

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_robust_json_parser():
    """Test robustního JSON parseru na průzkumu 548754"""
    
    print("🔍 Test robustního JSON parseru")
    print("=" * 50)
    
    # Cesta k JSON souboru
    json_file = "data/dotazniky.urad.online/548754/structure.json"
    
    if not os.path.exists(json_file):
        print(f"❌ JSON soubor neexistuje: {json_file}")
        return False
    
    try:
        # Inicializace parseru
        parser = RobustJSONParser()
        
        # Parsování JSON souboru
        print(f"📄 Parsování souboru: {json_file}")
        survey = parser.parse_json_file(json_file)
        
        if not survey:
            print("❌ Parsování selhalo")
            return False
        
        print("✅ Parsování úspěšné!")
        
        # Zobrazení základních informací
        print(f"\n📊 Metadata průzkumu:")
        print(f"   ID: {survey.metadata.sid}")
        print(f"   Název: {survey.metadata.title}")
        print(f"   Jazyk: {survey.metadata.language}")
        print(f"   Aktivní: {survey.metadata.active}")
        
        print(f"\n📁 Skupiny ({len(survey.groups)}):")
        for i, group in enumerate(survey.groups[:5], 1):  # Prvních 5 skupin
            print(f"   {i}. [{group.gid}] {group.group_name} ({len(group.questions)} otázek)")
        
        if len(survey.groups) > 5:
            print(f"   ... a dalších {len(survey.groups) - 5} skupin")
        
        print(f"\n❓ Celkem otázek: {len(survey.questions)}")
        
        # Analýza typů otázek
        type_counts = parser.get_question_types_summary(survey)
        print(f"\n📋 Typy otázek:")
        for qtype, count in sorted(type_counts.items()):
            print(f"   {qtype}: {count}")
        
        # Export do vylepšeného JSON
        output_file = "data/dotazniky.urad.online/548754/structure_enhanced_test.json"
        if parser.export_to_json(survey, output_file):
            print(f"\n💾 Vylepšená struktura exportována: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_json_structure():
    """Test struktury JSON souboru"""
    
    print("\n🔍 Analýza struktury JSON souboru")
    print("=" * 50)
    
    json_file = "data/dotazniky.urad.online/548754/structure.json"
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📄 Klíče v root objektu: {list(data.keys())}")
        
        if 'groups' in data:
            groups = data['groups']
            print(f"📁 Počet skupin: {len(groups)}")
            
            if groups:
                first_group = groups[0]
                print(f"📋 Klíče první skupiny: {list(first_group.keys())}")
                
                if 'questions' in first_group:
                    questions = first_group['questions']
                    print(f"❓ Počet otázek v první skupině: {len(questions)}")
                    
                    if questions:
                        first_question = questions[0]
                        print(f"🔍 Klíče první otázky: {list(first_question.keys())}")
                        
                        if 'properties' in first_question:
                            props = first_question['properties']
                            print(f"⚙️ Klíče properties: {list(props.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při analýze struktury: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Spouštím test robustního JSON parseru")
    print("📊 Průzkum: 548754")
    print("=" * 50)
    
    # Test struktury JSON
    test_json_structure()
    
    # Test parseru
    success = test_robust_json_parser()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test dokončen úspěšně!")
    else:
        print("⚠️ Test dokončen s chybami")
    print("=" * 50)
