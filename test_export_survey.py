#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test export_survey API volání podle tvého kódu
"""

import requests
import json
import base64
import os
from dotenv import load_dotenv

load_dotenv()

class LimeSurveyAPI:
    def __init__(self, url, username, password):
        self.url = url
        self.username = username
        self.password = password
        self.session_key = None
    
    def get_session_key(self):
        """Získá session key pro autentifikaci"""
        data = {
            'method': 'get_session_key',
            'params': {
                'username': self.username,
                'password': self.password
            },
            'id': 1,
            'jsonrpc': '2.0'
        }
        
        response = requests.post(self.url, json=data)
        result = response.json()
        
        if 'result' in result:
            self.session_key = result['result']
            print(f"✅ Session key získán: {self.session_key[:10]}...")
            return self.session_key
        else:
            raise Exception(f"Chyba p<PERSON>i z<PERSON>vání session key: {result}")
    
    def export_survey_structure(self, survey_id):
        """Exportuje strukturu průzkumu ve formátu LSS (XML)"""
        if not self.session_key:
            self.get_session_key()
        
        print(f"📤 Volám export_survey pro průzkum {survey_id}")
        
        data = {
            'method': 'export_survey',
            'params': {
                'sSessionKey': self.session_key,
                'iSurveyID': survey_id,
                'sExportType': 'lss',
                'sExportLanguage': 'cs',
                'sSurveyPassword': '',
                'bExportResources': True
            },
            'id': 1,
            'jsonrpc': '2.0'
        }
        
        response = requests.post(self.url, json=data)

        print(f"📋 Response status: {response.status_code}")
        print(f"📋 Response headers: {dict(response.headers)}")
        print(f"📋 Raw response (prvních 500 znaků): {response.text[:500]}")

        try:
            result = response.json()
            print(f"📋 Response keys: {list(result.keys()) if isinstance(result, dict) else 'Not dict'}")
            return result
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            print(f"📄 Full response text: {response.text}")
            return None
    
    def save_lss_file(self, survey_id, filename=None):
        """Stáhne a uloží LSS soubor"""
        if not filename:
            filename = f"survey_{survey_id}.lss"
        
        # Exportuje průzkum
        export_result = self.export_survey_structure(survey_id)
        
        print(f"📊 Export result: {export_result}")
        
        if 'result' in export_result and export_result['result']:
            # Dekóduje base64 obsah
            try:
                lss_content = base64.b64decode(export_result['result'])
                
                # Uloží do souboru
                with open(filename, 'wb') as f:
                    f.write(lss_content)
                
                print(f"✅ LSS soubor uložen jako: {filename}")
                
                # Zkontroluje obsah
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read(500)  # Prvních 500 znaků
                    print(f"📄 Obsah souboru (prvních 500 znaků):")
                    print(content)
                
                return filename
            except Exception as e:
                print(f"❌ Chyba při dekódování: {e}")
                return None
        else:
            print(f"❌ Chyba při exportu průzkumu: {export_result}")
            return None
    
    def release_session_key(self):
        """Uvolní session key"""
        if self.session_key:
            data = {
                'method': 'release_session_key',
                'params': {
                    'sSessionKey': self.session_key
                },
                'id': 1,
                'jsonrpc': '2.0'
            }
            
            response = requests.post(self.url, json=data)
            result = response.json()
            print(f"🔓 Session key uvolněn: {result}")
            self.session_key = None

def main():
    print("🚀 Test export_survey API volání")
    print("=" * 50)
    
    # Konfigurace z .env
    api_url = os.getenv('LIMESURVEY_API_URL')
    username = os.getenv('LIMESURVEY_USERNAME')
    password = os.getenv('LIMESURVEY_PASSWORD')
    survey_id = 548754
    
    print(f"🌐 API URL: {api_url}")
    print(f"👤 Username: {username}")
    print(f"📊 Survey ID: {survey_id}")
    
    if not all([api_url, username, password]):
        print("❌ Chybí konfigurace v .env")
        return
    
    # Vytvoření instance API
    api = LimeSurveyAPI(api_url, username, password)
    
    try:
        # Získání session key
        api.get_session_key()
        
        # Stažení LSS souboru (struktura průzkumu v XML)
        print("\n=== Stahování LSS souboru ===")
        lss_filename = api.save_lss_file(survey_id, f"test_survey_{survey_id}.lss")
        
        if lss_filename:
            print(f"🎉 Úspěch! LSS soubor: {lss_filename}")
        else:
            print("❌ Export se nezdařil")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
    
    finally:
        # Uvolnění session key
        api.release_session_key()

if __name__ == "__main__":
    main()
