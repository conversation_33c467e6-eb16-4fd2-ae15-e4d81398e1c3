"""
Privacy Filter - Centrální filtrování citlivých sloupců
Umožňuje uživateli vybrat sloupce CSV, které se mají vyloučit ze zpracování
"""

import os
import json
import pandas as pd
from typing import List, Dict, Set, Optional
from logger import get_logger

logger = get_logger(__name__)

class PrivacyFilter:
    """Centrální správce filtrování citlivých sloupců"""
    
    def __init__(self, survey_id: str):
        self.survey_id = survey_id
        # Použití PathManageru pro správné cesty
        from path_manager import path_manager
        self.survey_dir = path_manager.get_data_path(survey_id)
        self.filter_file = os.path.join(self.survey_dir, "privacy_filter.json")
        self.excluded_columns: Set[int] = set()
        self.load_filter_settings()
    
    def load_filter_settings(self) -> bool:
        """Načte nastavení filtru ze souboru"""
        try:
            if os.path.exists(self.filter_file):
                with open(self.filter_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.excluded_columns = set(data.get('excluded_columns', []))
                    logger.info(f"Načteno privacy nastavení: vyloučeno {len(self.excluded_columns)} sloupců")
                    return True
        except Exception as e:
            logger.error(f"Chyba při načítání privacy filtru: {e}")
        
        self.excluded_columns = set()
        return False
    
    def save_filter_settings(self) -> bool:
        """Uloží nastavení filtru do souboru"""
        try:
            os.makedirs(self.survey_dir, exist_ok=True)
            data = {
                "survey_id": self.survey_id,
                "excluded_columns": sorted(list(self.excluded_columns)),
                "created": "user_configured",
                "description": "Sloupce vyloučené z zpracování kvůli ochraně soukromí"
            }
            
            with open(self.filter_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Privacy nastavení uloženo: {len(self.excluded_columns)} vyloučených sloupců")
            return True
            
        except Exception as e:
            logger.error(f"Chyba při ukládání privacy filtru: {e}")
            return False
    
    def _load_question_mapping(self) -> Dict[str, str]:
        """Načte mapování technických názvů na lidsky čitelné názvy z question_mapping.csv"""
        mapping = {}
        try:
            mapping_path = os.path.join(self.survey_dir, "question_mapping.csv")
            if os.path.exists(mapping_path):
                import csv
                with open(mapping_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        code = row.get('question_code', '')
                        name = row.get('question_name', '')
                        if code and name:
                            mapping[code] = name
                logger.debug(f"Načteno {len(mapping)} mapování otázek")
            else:
                logger.warning(f"Question mapping soubor neexistuje: {mapping_path}")
        except Exception as e:
            logger.error(f"Chyba při načítání question mapping: {e}")

        return mapping

    def _get_human_readable_name(self, column_name: str, question_mapping: Dict[str, str]) -> str:
        """Převede technický název sloupce na lidsky čitelný"""
        # Pokud je to otázka (začíná na G), zkusíme najít v mapování
        if column_name.startswith('G') and column_name in question_mapping:
            return question_mapping[column_name]

        # Pro systémové sloupce vytvoříme popisný název
        system_columns = {
            'id': 'ID respondenta',
            'submitdate': 'Datum odeslání',
            'lastpage': 'Poslední stránka',
            'startlanguage': 'Jazyk průzkumu',
            'seed': 'Seed',
            'startdate': 'Datum zahájení',
            'datestamp': 'Časové razítko',
            'ipaddr': 'IP adresa',
            'refurl': 'Referenční URL'
        }

        if column_name.lower() in system_columns:
            return system_columns[column_name.lower()]

        # Fallback - vrátíme původní název
        return column_name

    def get_csv_columns_info(self, csv_path: str) -> List[Dict]:
        """Vrátí informace o všech sloupcích v CSV s lidsky čitelnými názvy"""
        try:
            # Načteme pouze hlavičku
            df = pd.read_csv(csv_path, sep=';', nrows=0)
            columns_info = []

            # Načteme mapování technických názvů na lidsky čitelné
            question_mapping = self._load_question_mapping()

            for i, col_name in enumerate(df.columns):
                # Očistíme název sloupce od uvozovek
                clean_name = col_name.strip().strip('"')

                # Získáme lidsky čitelný název
                human_readable_name = self._get_human_readable_name(clean_name, question_mapping)

                columns_info.append({
                    'index': i + 1,  # Číslování od 1 pro uživatele
                    'name': clean_name,  # Technický název
                    'human_name': human_readable_name,  # Lidsky čitelný název
                    'is_excluded': (i + 1) in self.excluded_columns
                })

            return columns_info

        except Exception as e:
            logger.error(f"Chyba při načítání sloupců CSV: {e}")
            return []
    
    def parse_column_range(self, range_string: str) -> Set[int]:
        """Parsuje řetězec s rozsahy sloupců (např. '1-4,8,64-78')"""
        columns = set()
        
        if not range_string.strip():
            return columns
        
        try:
            # Rozdělíme podle čárek
            parts = range_string.split(',')
            
            for part in parts:
                part = part.strip()
                if not part:
                    continue
                
                if '-' in part:
                    # Rozsah (např. 1-4)
                    start, end = part.split('-', 1)
                    start_num = int(start.strip())
                    end_num = int(end.strip())
                    
                    if start_num <= end_num:
                        columns.update(range(start_num, end_num + 1))
                    else:
                        logger.warning(f"Neplatný rozsah: {part} (start > end)")
                else:
                    # Jednotlivé číslo
                    columns.add(int(part))
            
            return columns
            
        except ValueError as e:
            logger.error(f"Chyba při parsování rozsahu sloupců '{range_string}': {e}")
            return set()
    
    def set_excluded_columns(self, range_string: str) -> bool:
        """Nastaví vyloučené sloupce podle řetězce rozsahu"""
        try:
            new_excluded = self.parse_column_range(range_string)
            self.excluded_columns = new_excluded
            
            logger.info(f"Nastaveno {len(self.excluded_columns)} vyloučených sloupců: {sorted(self.excluded_columns)}")
            return self.save_filter_settings()
            
        except Exception as e:
            logger.error(f"Chyba při nastavování vyloučených sloupců: {e}")
            return False
    
    def filter_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Odfiltruje vyloučené sloupce z DataFrame"""
        if not self.excluded_columns:
            return df
        
        try:
            # Převedeme indexy sloupců (1-based) na 0-based
            excluded_indices = [i - 1 for i in self.excluded_columns if 0 <= i - 1 < len(df.columns)]
            
            if excluded_indices:
                # Získáme názvy sloupců k vyloučení
                excluded_names = [df.columns[i] for i in excluded_indices]
                
                # Odfiltrujeme sloupce
                filtered_df = df.drop(columns=excluded_names)
                
                logger.info(f"Odfiltrováno {len(excluded_names)} sloupců z DataFrame")
                logger.debug(f"Vyloučené sloupce: {excluded_names}")
                
                return filtered_df
            
            return df
            
        except Exception as e:
            logger.error(f"Chyba při filtrování DataFrame: {e}")
            return df
    
    def filter_csv_file(self, input_path: str, output_path: str = None) -> bool:
        """Vytvoří filtrovanou verzi CSV souboru"""
        if not self.excluded_columns:
            logger.info("Žádné sloupce k vyloučení - kopíruji původní soubor")
            if output_path and input_path != output_path:
                import shutil
                shutil.copy2(input_path, output_path)
            return True
        
        try:
            # Načteme CSV
            df = pd.read_csv(input_path, sep=';')
            
            # Aplikujeme filtr
            filtered_df = self.filter_dataframe(df)
            
            # Uložíme filtrovaný soubor
            if output_path is None:
                output_path = input_path.replace('.csv', '_filtered.csv')
            
            filtered_df.to_csv(output_path, sep=';', index=False)
            
            logger.info(f"Filtrovaný CSV uložen: {output_path}")
            logger.info(f"Původní sloupců: {len(df.columns)}, po filtrování: {len(filtered_df.columns)}")
            
            return True
            
        except Exception as e:
            logger.error(f"Chyba při filtrování CSV souboru: {e}")
            return False
    
    def get_excluded_columns_list(self) -> List[int]:
        """Vrátí seznam vyloučených sloupců (seřazený)"""
        return sorted(list(self.excluded_columns))
    
    def get_filter_summary(self, csv_path: str = None) -> Dict:
        """Vrátí souhrn nastavení filtru"""
        summary = {
            'survey_id': self.survey_id,
            'excluded_count': len(self.excluded_columns),
            'excluded_columns': self.get_excluded_columns_list(),
            'filter_active': len(self.excluded_columns) > 0
        }
        
        if csv_path and os.path.exists(csv_path):
            try:
                df = pd.read_csv(csv_path, sep=';', nrows=0)
                summary['total_columns'] = len(df.columns)
                summary['processed_columns'] = len(df.columns) - len(self.excluded_columns)
                
                # Názvy vyloučených sloupců
                excluded_indices = [i - 1 for i in self.excluded_columns if 0 <= i - 1 < len(df.columns)]
                summary['excluded_column_names'] = [df.columns[i] for i in excluded_indices]
                
            except Exception as e:
                logger.warning(f"Nepodařilo se načíst informace o CSV: {e}")
        
        return summary
    
    def clear_filter(self) -> bool:
        """Vymaže všechna nastavení filtru"""
        self.excluded_columns = set()
        return self.save_filter_settings()
    
    def is_column_excluded(self, column_index: int) -> bool:
        """Zkontroluje, zda je sloupec vyloučen (1-based indexing)"""
        return column_index in self.excluded_columns

    def find_textual_questions(self, csv_path: str) -> List[Dict]:
        """Najde všechny hlavní textové otázky (typ T a S) pro vyloučení"""
        try:
            from path_manager import path_manager

            # Načteme question_mapping.csv pro identifikaci typů otázek
            mapping_path = path_manager.get_data_path(self.survey_id, 'question_mapping.csv')

            if not os.path.exists(mapping_path):
                logger.warning(f"Question mapping neexistuje: {mapping_path}")
                return []

            # OPRAVA: Načteme mapování s čárkou jako separátorem
            import pandas as pd
            mapping_df = pd.read_csv(mapping_path, sep=',')

            # OPRAVA: Najdeme textové otázky (typ T = dlouhý text, S = krátký text)
            # Hlavní otázky poznáme podle is_main_question=True
            textual_questions = mapping_df[
                (mapping_df['question_type'].isin(['T', 'S'])) &  # Textové typy
                (mapping_df['is_main_question'] == True)  # Hlavní otázky (ne subquestions)
            ]

            # Získáme informace o sloupcích CSV
            columns_info = self.get_csv_columns_info(csv_path)

            # Najdeme odpovídající sloupce v CSV
            textual_columns = []
            for _, question in textual_questions.iterrows():
                question_code = question['question_code']

                # Najdeme sloupec v CSV podle kódu
                for col_info in columns_info:
                    # Kontrola podle technického názvu (obsahuje kód otázky)
                    if question_code in col_info['name']:
                        # Ověříme, že je to hlavní otázka (ne subquestion)
                        # Hlavní textové otázky obvykle končí 'q0' nebo jsou bez suffixu
                        if col_info['name'].endswith('q0') or col_info['name'] == question_code:
                            textual_columns.append(col_info)
                            break

            logger.info(f"Nalezeno {len(textual_columns)} textových otázek pro vyloučení")
            return textual_columns

        except Exception as e:
            logger.error(f"Chyba při hledání textových otázek: {e}")
            return []


def get_privacy_filter(survey_id: str) -> PrivacyFilter:
    """Factory funkce pro získání privacy filtru"""
    return PrivacyFilter(survey_id)


# Centrální funkce pro filtrování - použití v ostatních modulech
def apply_privacy_filter(df: pd.DataFrame, survey_id: str) -> pd.DataFrame:
    """Centrální funkce pro aplikaci privacy filtru na DataFrame"""
    privacy_filter = get_privacy_filter(survey_id)
    return privacy_filter.filter_dataframe(df)


def filter_csv_with_privacy(input_path: str, survey_id: str, output_path: str = None) -> bool:
    """Centrální funkce pro filtrování CSV souboru"""
    privacy_filter = get_privacy_filter(survey_id)
    return privacy_filter.filter_csv_file(input_path, output_path)