"""
Diagnostické funkce pro analýzu struktury průzkumu
"""

import json
import os
import csv
from collections import defaultdict, Counter
import logging

logger = logging.getLogger(__name__)


def analyze_lss_structure(lss_path):
    """Analýza LSS struktury - skupiny a otázky"""
    if not os.path.exists(lss_path):
        print(f"❌ LSS soubor neexistuje: {lss_path}")
        return
    
    try:
        with open(lss_path, 'r', encoding='utf-8') as f:
            structure = json.load(f)
        
        print("\n🏗️ ANALÝZA LSS STRUKTURY")
        print("=" * 50)
        
        survey_id = structure.get('survey_id', 'N/A')
        groups = structure.get('groups', [])
        
        print(f"Survey ID: {survey_id}")
        print(f"Celkem skupin: {len(groups)}")
        
        # Analýza skupin
        group_questions = {}
        question_types = Counter()
        question_texts = defaultdict(list)
        
        for group in groups:
            gid = group.get('gid')
            group_name = group.get('group_name', '')
            group_order = group.get('group_order', 0)
            questions = group.get('questions', [])
            
            group_questions[gid] = {
                'name': group_name,
                'order': group_order,
                'question_count': len(questions)
            }
            
            print(f"\n📁 Skupina {group_order}: '{group_name}' (GID: {gid})")
            print(f"   Počet otázek: {len(questions)}")
            
            # Analýza otázek ve skupině
            for question in questions:
                qtype = question.get('type', '')
                qtext = question.get('question', '')
                qtitle = question.get('title', '')
                
                question_types[qtype] += 1
                
                if qtext and qtext not in ['', ' ']:
                    question_texts[qtext].append({
                        'group': group_name,
                        'title': qtitle,
                        'type': qtype
                    })
                
                print(f"     - {qtitle}: {qtext[:50]}{'...' if len(qtext) > 50 else ''} (typ: {qtype})")
        
        # Souhrn typů otázek
        print(f"\n📊 SOUHRN TYPŮ OTÁZEK:")
        for qtype, count in question_types.most_common():
            print(f"   {qtype}: {count}x")
        
        # Detekce opakujících se otázek
        print(f"\n🔍 OPAKUJÍCÍ SE OTÁZKY:")
        repeating_found = False
        for qtext, occurrences in question_texts.items():
            if len(occurrences) > 1:
                repeating_found = True
                print(f"\n   '{qtext[:60]}{'...' if len(qtext) > 60 else ''}'")
                print(f"   Vyskytuje se {len(occurrences)}x:")
                for occ in occurrences:
                    print(f"     - {occ['group']} ({occ['title']}, typ: {occ['type']})")
        
        if not repeating_found:
            print("   Žádné opakující se otázky nebyly nalezeny")
            
    except Exception as e:
        print(f"❌ Chyba při analýze LSS: {e}")


def analyze_csv_columns(csv_path):
    """Analýza CSV sloupců"""
    if not os.path.exists(csv_path):
        print(f"❌ CSV soubor neexistuje: {csv_path}")
        return
    
    try:
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=';')
            headers = next(reader)
        
        print("\n📊 ANALÝZA CSV SLOUPCŮ")
        print("=" * 50)
        
        print(f"Celkem sloupců: {len(headers)}")
        
        # Kategorizace sloupců
        system_cols = []
        question_cols = []
        other_cols = []
        
        question_patterns = {}
        
        for i, header in enumerate(headers):
            header = header.strip('"')
            
            if header in ['id', 'submitdate', 'lastpage', 'startlanguage', 'seed', 'startdate', 'datestamp', 'ipaddr', 'refurl']:
                system_cols.append((i, header))
            elif header.startswith('G') and ('Q' in header or '[' in header):
                question_cols.append((i, header))
                
                # Extrakce základního vzoru otázky
                base_pattern = header.split('[')[0] if '[' in header else header
                if base_pattern not in question_patterns:
                    question_patterns[base_pattern] = []
                question_patterns[base_pattern].append(header)
            elif '[' in header and ']' in header:
                question_cols.append((i, header))
                
                # Extrakce základního vzoru otázky
                base_pattern = header.split('[')[0] if '[' in header else header
                if base_pattern not in question_patterns:
                    question_patterns[base_pattern] = []
                question_patterns[base_pattern].append(header)
            else:
                other_cols.append((i, header))
        
        print(f"\n📋 KATEGORIE SLOUPCŮ:")
        print(f"   Systémové sloupce: {len(system_cols)}")
        print(f"   Sloupce otázek: {len(question_cols)}")
        print(f"   Ostatní sloupce: {len(other_cols)}")
        
        print(f"\n🔍 VZORY OTÁZEK:")
        for pattern, variants in question_patterns.items():
            if len(variants) > 1:
                print(f"   {pattern}: {len(variants)} variant")
                for variant in variants[:5]:  # Zobrazit max 5 variant
                    print(f"     - {variant}")
                if len(variants) > 5:
                    print(f"     ... a {len(variants) - 5} dalších")
            else:
                print(f"   {pattern}: 1 varianta")
        
        # Detekce možných problémů
        print(f"\n⚠️ MOŽNÉ PROBLÉMY:")
        
        # Kontrola duplicitních názvů
        header_counts = Counter(headers)
        duplicates = [h for h, count in header_counts.items() if count > 1]
        if duplicates:
            print(f"   Duplicitní názvy sloupců: {duplicates}")
        else:
            print("   ✅ Žádné duplicitní názvy sloupců")
        
        # Kontrola prázdných názvů
        empty_headers = [i for i, h in enumerate(headers) if not h.strip().strip('"')]
        if empty_headers:
            print(f"   Prázdné názvy sloupců na pozicích: {empty_headers}")
        else:
            print("   ✅ Žádné prázdné názvy sloupců")
            
    except Exception as e:
        print(f"❌ Chyba při analýze CSV: {e}")


def check_lss_csv_mapping(lss_path, csv_path, mapping_path):
    """Kontrola mapování mezi LSS a CSV"""
    print("\n🔗 KONTROLA MAPOVÁNÍ LSS ↔ CSV")
    print("=" * 50)
    
    # Načtení LSS struktury
    lss_questions = set()
    if os.path.exists(lss_path):
        try:
            with open(lss_path, 'r', encoding='utf-8') as f:
                structure = json.load(f)
            
            for group in structure.get('groups', []):
                for question in group.get('questions', []):
                    title = question.get('title', '')
                    if title:
                        lss_questions.add(title)
            
            print(f"LSS otázky: {len(lss_questions)} nalezeno")
        except Exception as e:
            print(f"❌ Chyba při načítání LSS: {e}")
            return
    else:
        print(f"❌ LSS soubor neexistuje: {lss_path}")
        return
    
    # Načtení CSV sloupců
    csv_columns = set()
    if os.path.exists(csv_path):
        try:
            with open(csv_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f, delimiter=';')
                headers = next(reader)
                csv_columns = set(h.strip('"') for h in headers)
            
            print(f"CSV sloupce: {len(csv_columns)} nalezeno")
        except Exception as e:
            print(f"❌ Chyba při načítání CSV: {e}")
            return
    else:
        print(f"❌ CSV soubor neexistuje: {csv_path}")
        return
    
    # Analýza mapování
    print(f"\n🔍 ANALÝZA MAPOVÁNÍ:")
    
    # Otázky v LSS ale ne v CSV
    lss_only = lss_questions - csv_columns
    if lss_only:
        print(f"\n❌ V LSS ale ne v CSV ({len(lss_only)}):")
        for q in sorted(list(lss_only)[:10]):  # Zobrazit max 10
            print(f"   - {q}")
        if len(lss_only) > 10:
            print(f"   ... a {len(lss_only) - 10} dalších")
    
    # Sloupce v CSV ale ne v LSS
    csv_only = csv_columns - lss_questions
    question_like_csv = [col for col in csv_only if any(c in col for c in ['Q', '[', 'q'])]
    
    if question_like_csv:
        print(f"\n❌ V CSV ale ne v LSS - otázky ({len(question_like_csv)}):")
        for q in sorted(question_like_csv[:10]):  # Zobrazit max 10
            print(f"   - {q}")
        if len(question_like_csv) > 10:
            print(f"   ... a {len(question_like_csv) - 10} dalších")
    
    # Společné otázky
    common = lss_questions & csv_columns
    print(f"\n✅ Společné otázky: {len(common)}")
    
    # Kontrola mapovacího souboru
    if os.path.exists(mapping_path):
        try:
            with open(mapping_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                mapping_codes = set(row['question_code'] for row in reader)
            
            print(f"\nMapovací soubor: {len(mapping_codes)} kódů")
            
            # Kódy v mapování ale ne v CSV
            mapping_only = mapping_codes - csv_columns
            if mapping_only:
                print(f"❌ V mapování ale ne v CSV ({len(mapping_only)}):")
                for code in sorted(list(mapping_only)[:5]):
                    print(f"   - {code}")
                if len(mapping_only) > 5:
                    print(f"   ... a {len(mapping_only) - 5} dalších")
            
        except Exception as e:
            print(f"❌ Chyba při načítání mapování: {e}")
    else:
        print(f"⚠️ Mapovací soubor neexistuje: {mapping_path}")


def detect_repeating_questions(lss_path):
    """Detekce opakujících se otázek v různých skupinách"""
    if not os.path.exists(lss_path):
        print(f"❌ LSS soubor neexistuje: {lss_path}")
        return
    
    try:
        with open(lss_path, 'r', encoding='utf-8') as f:
            structure = json.load(f)
        
        print("\n🔍 DETEKCE OPAKUJÍCÍCH SE OTÁZEK")
        print("=" * 50)
        
        question_texts = defaultdict(list)
        
        for group in structure.get('groups', []):
            group_name = group.get('group_name', '')
            gid = group.get('gid')
            
            for question in group.get('questions', []):
                qtext = question.get('question', '').strip()
                qtype = question.get('type', '')
                qtitle = question.get('title', '')
                
                if qtext and qtext not in ['', ' ']:
                    question_texts[qtext].append({
                        'group_name': group_name,
                        'group_id': gid,
                        'title': qtitle,
                        'type': qtype
                    })
        
        # Najít opakující se otázky
        repeating_questions = {text: occurrences for text, occurrences in question_texts.items() if len(occurrences) > 1}
        
        if repeating_questions:
            print(f"Nalezeno {len(repeating_questions)} opakujících se otázek:")
            
            for qtext, occurrences in repeating_questions.items():
                print(f"\n📝 '{qtext}'")
                print(f"   Vyskytuje se {len(occurrences)}x:")
                
                for occ in occurrences:
                    print(f"     - Skupina: {occ['group_name']} (ID: {occ['group_id']})")
                    print(f"       Kód: {occ['title']}, Typ: {occ['type']}")
        else:
            print("✅ Žádné opakující se otázky nebyly nalezeny")
            
    except Exception as e:
        print(f"❌ Chyba při detekci: {e}")


def check_translations(translations_path, mapping_path):
    """Kontrola překladů"""
    print("\n🌍 KONTROLA PŘEKLADŮ")
    print("=" * 50)

    if not os.path.exists(translations_path):
        print(f"❌ Soubor překladů neexistuje: {translations_path}")
        return

    try:
        with open(translations_path, 'r', encoding='utf-8') as f:
            translations = json.load(f)

        question_names = translations.get('question_names', {})
        subquestions = translations.get('subquestions', {})

        print(f"Přeložené názvy otázek: {len(question_names)}")
        print(f"Přeložené podotázky: {len(subquestions)}")

        # Kontrola technických kódů v překladech
        technical_codes = []
        for key, value in subquestions.items():
            if 'SQ0' in value or 'SQ9' in value:
                technical_codes.append((key, value))

        if technical_codes:
            print(f"\n⚠️ TECHNICKÉ KÓDY V PŘEKLADECH ({len(technical_codes)}):")
            for key, value in technical_codes[:5]:
                print(f"   {key}: {value}")
            if len(technical_codes) > 5:
                print(f"   ... a {len(technical_codes) - 5} dalších")
        else:
            print("✅ Žádné technické kódy v překladech")

        # Kontrola chybějících překladů pro lokality
        locality_questions = []
        for key, value in subquestions.items():
            if 'lokalit' in value.lower():
                locality_questions.append((key, value))

        print(f"\n🏘️ OTÁZKY O LOKALITÁCH ({len(locality_questions)}):")
        for key, value in locality_questions[:5]:
            print(f"   {key}: {value}")
        if len(locality_questions) > 5:
            print(f"   ... a {len(locality_questions) - 5} dalších")

    except Exception as e:
        print(f"❌ Chyba při kontrole překladů: {e}")


def generate_complete_diagnostic_report(lss_path, csv_path, mapping_path, translations_path):
    """Generování kompletního diagnostického reportu"""
    print("\n📋 KOMPLETNÍ DIAGNOSTICKÝ REPORT")
    print("=" * 60)

    report_path = os.path.join(os.path.dirname(lss_path), 'diagnostic_report.md')

    try:
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# Diagnostický Report Struktury Průzkumu\n\n")
            f.write(f"Generováno: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Základní informace
            f.write("## Základní Informace\n\n")
            f.write(f"- LSS soubor: {lss_path}\n")
            f.write(f"- CSV soubor: {csv_path}\n")
            f.write(f"- Mapování: {mapping_path}\n")
            f.write(f"- Překlady: {translations_path}\n\n")

            # Analýza LSS
            if os.path.exists(lss_path):
                with open(lss_path, 'r', encoding='utf-8') as lss_f:
                    structure = json.load(lss_f)

                f.write("## Analýza LSS Struktury\n\n")
                f.write(f"- Survey ID: {structure.get('survey_id', 'N/A')}\n")
                f.write(f"- Počet skupin: {len(structure.get('groups', []))}\n\n")

                # Detailní analýza skupin
                f.write("### Skupiny a Otázky\n\n")
                question_texts = defaultdict(list)

                for group in structure.get('groups', []):
                    group_name = group.get('group_name', '')
                    questions = group.get('questions', [])
                    f.write(f"**{group_name}** ({len(questions)} otázek)\n")

                    for question in questions:
                        qtext = question.get('question', '')
                        qtitle = question.get('title', '')
                        qtype = question.get('type', '')

                        if qtext and qtext.strip():
                            question_texts[qtext].append({
                                'group': group_name,
                                'title': qtitle,
                                'type': qtype
                            })

                        f.write(f"- {qtitle}: {qtext[:50]}{'...' if len(qtext) > 50 else ''} (typ: {qtype})\n")
                    f.write("\n")

                # Opakující se otázky
                f.write("### Opakující se Otázky\n\n")
                repeating = {text: occs for text, occs in question_texts.items() if len(occs) > 1}

                if repeating:
                    for qtext, occurrences in repeating.items():
                        f.write(f"**{qtext}** ({len(occurrences)}x)\n")
                        for occ in occurrences:
                            f.write(f"- {occ['group']} ({occ['title']}, {occ['type']})\n")
                        f.write("\n")
                else:
                    f.write("Žádné opakující se otázky nebyly nalezeny.\n\n")

            # Analýza CSV
            if os.path.exists(csv_path):
                with open(csv_path, 'r', encoding='utf-8') as csv_f:
                    reader = csv.reader(csv_f, delimiter=';')
                    headers = next(reader)

                f.write("## Analýza CSV\n\n")
                f.write(f"- Celkem sloupců: {len(headers)}\n")

                question_cols = [h for h in headers if 'Q' in h or '[' in h]
                f.write(f"- Sloupce otázek: {len(question_cols)}\n\n")

                # Vzory otázek
                patterns = defaultdict(list)
                for header in question_cols:
                    base = header.split('[')[0] if '[' in header else header
                    patterns[base].append(header)

                f.write("### Vzory Otázek\n\n")
                for pattern, variants in patterns.items():
                    if len(variants) > 1:
                        f.write(f"**{pattern}**: {len(variants)} variant\n")
                        for variant in variants[:3]:
                            f.write(f"- {variant}\n")
                        if len(variants) > 3:
                            f.write(f"- ... a {len(variants) - 3} dalších\n")
                        f.write("\n")

        print(f"✅ Report uložen do: {report_path}")

    except Exception as e:
        print(f"❌ Chyba při generování reportu: {e}")


def generate_corrected_mapping(lss_path, csv_path):
    """Generování opraveného mapování"""
    print("\n🛠️ GENEROVÁNÍ OPRAVENÉHO MAPOVÁNÍ")
    print("=" * 50)

    if not os.path.exists(lss_path) or not os.path.exists(csv_path):
        print("❌ Chybí LSS nebo CSV soubor")
        return

    try:
        # Načtení LSS struktury
        with open(lss_path, 'r', encoding='utf-8') as f:
            structure = json.load(f)

        # Načtení CSV sloupců
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=';')
            headers = [h.strip('"') for h in next(reader)]

        # Vytvoření mapování
        corrected_mapping = []

        # Mapování podle skupin a lokalit
        locality_groups = {}
        for group in structure.get('groups', []):
            group_name = group.get('group_name', '')
            if any(locality in group_name.lower() for locality in ['modřany', 'beránek', 'kamýk', 'komořany', 'cholupice', 'točná']):
                locality_groups[group_name] = group

        print(f"Nalezeno {len(locality_groups)} skupin s lokalitami")

        # Hledání odpovídajících sloupců v CSV
        for group_name, group in locality_groups.items():
            print(f"\nZpracovávám skupinu: {group_name}")

            for question in group.get('questions', []):
                qtext = question.get('question', '')
                qtype = question.get('type', '')
                qtitle = question.get('title', '')

                if qtext and qtext.strip():
                    # Hledání odpovídajících sloupců v CSV
                    matching_cols = []
                    for header in headers:
                        if any(pattern in header for pattern in [qtitle, qtext[:20]]):
                            matching_cols.append(header)

                    if matching_cols:
                        for col in matching_cols:
                            corrected_mapping.append({
                                'question_code': col,
                                'question_name': f"{qtext} ({group_name})",
                                'question_text': qtext,
                                'is_main_question': '[' not in col,
                                'group_name': group_name,
                                'original_title': qtitle,
                                'question_type': qtype
                            })

        # Uložení opraveného mapování
        output_path = os.path.join(os.path.dirname(csv_path), 'question_mapping_corrected.csv')

        with open(output_path, 'w', encoding='utf-8', newline='') as f:
            if corrected_mapping:
                fieldnames = corrected_mapping[0].keys()
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(corrected_mapping)

        print(f"✅ Opravené mapování uloženo: {output_path}")
        print(f"   Celkem záznamů: {len(corrected_mapping)}")

    except Exception as e:
        print(f"❌ Chyba při generování mapování: {e}")
