# -*- coding: utf-8 -*-

"""
LimeSurvey Structure (LSS) Parser - Robustní XML parser

Tento modul poskytuje funkcionalitu pro parsování souborů se strukturou průzkumu LimeSurvey (LSS) ve formátu XML.
Extrahuje metadata průzkumu, struktur<PERSON> ot<PERSON>k, skupin a zpracovává různé verze LSS schématu.

Klíčové vlastnosti:
- Parsování metadat průzkumu (název, popis, jazyky atd.)
- Extrakce skupin otázek a jejich seřazení dle pořadí v průzkumu
- Extrakce otázek, v<PERSON><PERSON><PERSON><PERSON> jejich textů, typů a nastavení (např. povinnost)
- Správné z<PERSON> pod<PERSON> (subquestions), které jsou v LSS formátu definovány jako standardní otázky s vazbou na rodičovskou otázku
- Na<PERSON><PERSON><PERSON><PERSON><PERSON> předdefinovaných odpově<PERSON> (answers) a jejich přiřazení k otázkám
- Parsování a přiřazení pokročilých atributů otázek (question_attributes)
- Sestavení finální datové struktury, která hierarchicky odpovídá vzhledu průzkumu pro respondenta
- Robustní zpracování všech typů otázek LimeSurvey

Autor: Pavel Roušar
Datum: Prosinec 2024
Integrace do LIMWRAPP: Leden 2025
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Any, Union
import json
import logging
from pathlib import Path
from dataclasses import dataclass, asdict
import re
from datetime import datetime

# Nastavení loggeru
logger = logging.getLogger(__name__)

# --- Datové třídy (Dataclasses) ---
# Tyto třídy definují strukturu, do které se parsovaná data z XML ukládají.
# Použití dataclasses zjednodušuje vytváření objektů a jejich reprezentaci.

@dataclass
class SurveyMetadata:
    """Struktura pro metadata celého průzkumu."""
    sid: str
    title: str
    description: Optional[str]
    language: str
    created: Optional[str]
    modified: Optional[str]
    active: bool
    expires: Optional[str]
    startdate: Optional[str]

@dataclass
class QuestionAttribute:
    """
    Struktura pro atributy otázky.
    OPRAVENO: Přidáno pole 'qid', aby bylo možné atribut správně přiřadit k otázce.
    """
    qid: str
    name: str
    value: str
    language: Optional[str] = None

@dataclass
class Answer:
    """Struktura pro jednu předdefinovanou odpověď (např. u otázek s výběrem možností)."""
    qid: str
    code: str
    text: str
    sortorder: int
    scale_id: int = 0
    language: str = 'cs'

@dataclass
class Subquestion:
    """Struktura pro podotázku (např. řádek v maticové otázce)."""
    qid: str
    parent_qid: str
    title: str
    text: str
    scale_id: int = 0
    sortorder: int = 0

@dataclass
class Question:
    """Hlavní struktura pro otázku. Obsahuje všechny související entity."""
    qid: str
    type: str
    title: str
    text: str
    help_text: Optional[str]
    mandatory: bool
    other: bool
    gid: str
    question_order: int
    relevance: str  # Podmínka zobrazení otázky
    scale_id: int = 0
    language: str = 'cs'
    subquestions: List[Subquestion] = None
    answers: List[Answer] = None
    attributes: Dict[str, str] = None
    
    def __post_init__(self):
        """Inicializuje seznamy, aby nebyly sdílené mezi instancemi."""
        if self.subquestions is None:
            self.subquestions = []
        if self.answers is None:
            self.answers = []
        if self.attributes is None:
            self.attributes = {}

@dataclass
class Group:
    """Struktura pro skupinu otázek."""
    gid: str
    group_name: str
    group_order: int
    description: Optional[str]
    language: str = 'cs'
    questions: List[Question] = None
    
    def __post_init__(self):
        """Inicializuje seznam otázek."""
        if self.questions is None:
            self.questions = []

@dataclass
class Survey:
    """Hlavní struktura celého průzkumu."""
    metadata: SurveyMetadata
    groups: List[Group]
    questions: List[Question]
    
    def to_dict(self) -> Dict[str, Any]:
        """Převede survey na slovník pro JSON export."""
        return asdict(self)


class RobustLSSParser:
    """
    Robustní parser pro LimeSurvey LSS soubory.
    
    Tento parser je navržen pro zpracování všech typů otázek LimeSurvey
    a zachování přesné hierarchie a pořadí prvků.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def parse_lss_file(self, lss_file_path: str) -> Optional[Survey]:
        """
        Parsuje LSS soubor a vrátí strukturovaná data
        
        Args:
            lss_file_path: Cesta k LSS souboru
            
        Returns:
            Survey objekt nebo None při chybě
        """
        try:
            self.logger.info(f"🔍 Parsování LSS souboru: {lss_file_path}")
            
            # Kontrola existence souboru
            if not Path(lss_file_path).exists():
                self.logger.error(f"❌ LSS soubor neexistuje: {lss_file_path}")
                return None
            
            # Načtení a parsování XML
            tree = ET.parse(lss_file_path)
            root = tree.getroot()
            
            self.logger.info(f"📄 Načten XML LSS soubor")
            
            # Parsování metadat
            metadata = self._parse_metadata(root)
            if not metadata:
                self.logger.error("❌ Nepodařilo se načíst metadata průzkumu")
                return None
            
            # Parsování skupin
            groups = self._parse_groups(root)
            self.logger.info(f"📁 Načteno {len(groups)} skupin")
            
            # Parsování otázek
            questions = self._parse_questions(root)
            self.logger.info(f"❓ Načteno {len(questions)} otázek")
            
            # Přiřazení otázek do skupin
            self._assign_questions_to_groups(groups, questions)
            
            # Vytvoření finální struktury
            survey = Survey(
                metadata=metadata,
                groups=groups,
                questions=questions
            )
            
            self.logger.info(f"✅ Parsování dokončeno úspěšně")
            return survey
            
        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování LSS: {str(e)}")
            return None
    
    def _parse_metadata(self, root: ET.Element) -> Optional[SurveyMetadata]:
        """Parsuje metadata průzkumu z XML"""
        try:
            # Hledání survey elementu
            survey_elem = root.find('.//survey')
            if survey_elem is None:
                self.logger.error("❌ Nenalezen survey element v LSS")
                return None
            
            # Extrakce základních údajů
            sid = survey_elem.get('sid', '')
            
            # Hledání dalších metadat
            title = self._get_text_safe(survey_elem, 'surveyls_title', 'Bez názvu')
            description = self._get_text_safe(survey_elem, 'surveyls_description')
            language = self._get_text_safe(survey_elem, 'language', 'cs')
            created = self._get_text_safe(survey_elem, 'datecreated')
            modified = self._get_text_safe(survey_elem, 'datemodified')
            active = self._get_text_safe(survey_elem, 'active', 'N') == 'Y'
            expires = self._get_text_safe(survey_elem, 'expires')
            startdate = self._get_text_safe(survey_elem, 'startdate')
            
            return SurveyMetadata(
                sid=sid,
                title=title,
                description=description,
                language=language,
                created=created,
                modified=modified,
                active=active,
                expires=expires,
                startdate=startdate
            )
            
        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování metadat: {str(e)}")
            return None
    
    def _get_text_safe(self, element: ET.Element, tag: str, default: str = None) -> Optional[str]:
        """Bezpečně získá text z XML elementu"""
        try:
            found = element.find(tag)
            if found is not None and found.text:
                return found.text.strip()
            return default
        except:
            return default

    def _parse_groups(self, root: ET.Element) -> List[Group]:
        """Parsuje skupiny otázek z XML"""
        groups = []
        try:
            # Hledání všech skupin
            for group_elem in root.findall('.//group'):
                group = self._parse_single_group(group_elem)
                if group:
                    groups.append(group)

            # Seřazení podle group_order
            groups.sort(key=lambda g: g.group_order)

            return groups

        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování skupin: {str(e)}")
            return []

    def _parse_single_group(self, group_elem: ET.Element) -> Optional[Group]:
        """Parsuje jednu skupinu z XML elementu"""
        try:
            gid = group_elem.get('gid', '')
            if not gid:
                return None

            group_name = self._get_text_safe(group_elem, 'group_name', f'Skupina {gid}')
            group_order = int(self._get_text_safe(group_elem, 'group_order', '0'))
            description = self._get_text_safe(group_elem, 'description')
            language = self._get_text_safe(group_elem, 'language', 'cs')

            return Group(
                gid=gid,
                group_name=group_name,
                group_order=group_order,
                description=description,
                language=language
            )

        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování skupiny: {str(e)}")
            return None

    def _parse_questions(self, root: ET.Element) -> List[Question]:
        """Parsuje všechny otázky z XML"""
        questions = []
        try:
            # Hledání všech otázek
            for question_elem in root.findall('.//question'):
                question = self._parse_single_question(question_elem)
                if question:
                    questions.append(question)

            # Seřazení podle question_order a gid
            questions.sort(key=lambda q: (q.gid, q.question_order))

            # Parsování subotázek a odpovědí
            self._parse_subquestions_and_answers(root, questions)

            return questions

        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování otázek: {str(e)}")
            return []

    def _parse_single_question(self, question_elem: ET.Element) -> Optional[Question]:
        """Parsuje jednu otázku z XML elementu"""
        try:
            qid = question_elem.get('qid', '')
            if not qid:
                return None

            # Základní údaje
            question_type = self._get_text_safe(question_elem, 'type', 'T')
            title = self._get_text_safe(question_elem, 'title', f'Q{qid}')
            text = self._get_text_safe(question_elem, 'question', '')
            help_text = self._get_text_safe(question_elem, 'help')
            gid = self._get_text_safe(question_elem, 'gid', '')
            question_order = int(self._get_text_safe(question_elem, 'question_order', '0'))
            relevance = self._get_text_safe(question_elem, 'relevance', '1')
            scale_id = int(self._get_text_safe(question_elem, 'scale_id', '0'))
            language = self._get_text_safe(question_elem, 'language', 'cs')

            # Boolean hodnoty
            mandatory = self._get_text_safe(question_elem, 'mandatory', 'N') == 'Y'
            other = self._get_text_safe(question_elem, 'other', 'N') == 'Y'

            return Question(
                qid=qid,
                type=question_type,
                title=title,
                text=text,
                help_text=help_text,
                mandatory=mandatory,
                other=other,
                gid=gid,
                question_order=question_order,
                relevance=relevance,
                scale_id=scale_id,
                language=language
            )

        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování otázky: {str(e)}")
            return None

    def _parse_subquestions_and_answers(self, root: ET.Element, questions: List[Question]):
        """Parsuje subotázky a odpovědi pro všechny otázky"""
        try:
            # Vytvoření mapy otázek pro rychlé vyhledávání
            question_map = {q.qid: q for q in questions}

            # Parsování subotázek
            for subq_elem in root.findall('.//subquestion'):
                subquestion = self._parse_single_subquestion(subq_elem)
                if subquestion and subquestion.parent_qid in question_map:
                    question_map[subquestion.parent_qid].subquestions.append(subquestion)

            # Parsování odpovědí
            for answer_elem in root.findall('.//answer'):
                answer = self._parse_single_answer(answer_elem)
                if answer and answer.qid in question_map:
                    question_map[answer.qid].answers.append(answer)

            # Seřazení subotázek a odpovědí podle sortorder
            for question in questions:
                question.subquestions.sort(key=lambda sq: sq.sortorder)
                question.answers.sort(key=lambda a: a.sortorder)

        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování subotázek a odpovědí: {str(e)}")

    def _parse_single_subquestion(self, subq_elem: ET.Element) -> Optional[Subquestion]:
        """Parsuje jednu subotázku z XML elementu"""
        try:
            qid = subq_elem.get('qid', '')
            parent_qid = self._get_text_safe(subq_elem, 'parent_qid', '')

            if not qid or not parent_qid:
                return None

            title = self._get_text_safe(subq_elem, 'title', f'SQ{qid}')
            text = self._get_text_safe(subq_elem, 'question', '')
            scale_id = int(self._get_text_safe(subq_elem, 'scale_id', '0'))
            sortorder = int(self._get_text_safe(subq_elem, 'question_order', '0'))

            return Subquestion(
                qid=qid,
                parent_qid=parent_qid,
                title=title,
                text=text,
                scale_id=scale_id,
                sortorder=sortorder
            )

        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování subotázky: {str(e)}")
            return None

    def _parse_single_answer(self, answer_elem: ET.Element) -> Optional[Answer]:
        """Parsuje jednu odpověď z XML elementu"""
        try:
            qid = self._get_text_safe(answer_elem, 'qid', '')
            code = self._get_text_safe(answer_elem, 'code', '')

            if not qid or not code:
                return None

            text = self._get_text_safe(answer_elem, 'answer', '')
            sortorder = int(self._get_text_safe(answer_elem, 'sortorder', '0'))
            scale_id = int(self._get_text_safe(answer_elem, 'scale_id', '0'))
            language = self._get_text_safe(answer_elem, 'language', 'cs')

            return Answer(
                qid=qid,
                code=code,
                text=text,
                sortorder=sortorder,
                scale_id=scale_id,
                language=language
            )

        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování odpovědi: {str(e)}")
            return None

    def _assign_questions_to_groups(self, groups: List[Group], questions: List[Question]):
        """Přiřadí otázky do příslušných skupin"""
        try:
            # Vytvoření mapy skupin
            group_map = {g.gid: g for g in groups}

            # Přiřazení otázek do skupin
            for question in questions:
                if question.gid in group_map:
                    group_map[question.gid].questions.append(question)
                else:
                    self.logger.warning(f"⚠️ Otázka {question.qid} nemá přiřazenou skupinu {question.gid}")

            # Seřazení otázek ve skupinách podle question_order
            for group in groups:
                group.questions.sort(key=lambda q: q.question_order)

        except Exception as e:
            self.logger.error(f"❌ Chyba při přiřazování otázek do skupin: {str(e)}")

    def get_question_types_summary(self, survey: Survey) -> Dict[str, int]:
        """Vrátí přehled typů otázek v průzkumu"""
        type_counts = {}
        for question in survey.questions:
            qtype = question.type
            type_counts[qtype] = type_counts.get(qtype, 0) + 1
        return type_counts

    def export_to_json(self, survey: Survey, output_path: str) -> bool:
        """Exportuje survey do JSON souboru"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(survey.to_dict(), f, ensure_ascii=False, indent=2)
            self.logger.info(f"✅ Survey exportován do: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Chyba při exportu do JSON: {str(e)}")
            return False
