# Analýza chyb v parseru LSS a zpracování dat - Průzkum 548754

## Hlavní problém: Nerozpoznávání opakujících se otázek pro různé lokality

### Popis problému
Ve zpracovávaném průzkumu 548754 se opakuje celkem 8 skupin se stejnými otázkami pro různé lokality:
- <PERSON><PERSON> (skupina 88)
- <PERSON><PERSON> (skupina 97) 
- <PERSON><PERSON><PERSON><PERSON> (skupina 98)
- <PERSON><PERSON> (skupina 99)
- <PERSON><PERSON><PERSON><PERSON> (skupina 100)
- <PERSON><PERSON><PERSON><PERSON> (skupina 101)
- Cholupice (skupina 102)
- <PERSON><PERSON><PERSON> (skupina 103)

Každá skupina obsahuje stejné otázky:
1. "Čeho si v této lokalitě nejvíce ceníte?"
2. "Co Vám v této lokalitě chybí?"
3. "J<PERSON> jste celkově spokojen/a s touto lokalitou?"
4. "<PERSON><PERSON><PERSON> vztah k lokalitě je:"

### Konkrétní chyby identifikované:

#### 1. **KRITICKÁ CHYBA: Nesprávné mapování otázek v CSV**
- **Problém**: V CSV jsou sloupce jako `r306q0[SQ001]`, `r664q0[SQ001]`, `r373q0[SQ001]`, atd.
- **Očekávané**: V question_mapping.csv jsou pouze `G3Q00001[SQ001]`, `G3Q00002[SQ001]`
- **Důsledek**: Parser nerozpoznává, že různé kódy otázek (r306q0, r664q0, atd.) jsou stejné otázky pro různé lokality
- **Lokace**: `src/data_transformer.py`, funkce `generate_question_mapping()`

#### 2. **CHYBA: Chybějící hlavní otázky v mapování**
- **Problém**: Hlavní otázky typu "Čeho si v této lokalitě nejvíce ceníte?" nejsou v question_mapping.csv jako hlavní otázky
- **Očekávané**: Měly by být 8 hlavních otázek (pro každou lokalitu jedna)
- **Skutečnost**: Jsou pouze subotázky G3Q00001[SQ001], G3Q00001[SQ002], atd.
- **Lokace**: `src/lss_parser.py`, funkce `_parse_hierarchical_questions()`

#### 3. **CHYBA: Nesprávná identifikace hlavních vs. subotázek**
- **Problém**: Parser nerozpoznává, že otázky pro různé lokality jsou hlavní otázky, ne subotázky
- **Důsledek**: V translations.json chybí správné názvy s lokalitami
- **Lokace**: `src/lss_parser.py`, funkce `_attach_subquestions_and_answers()`

#### 4. **CHYBA: Nesprávné generování překladů**
- **Problém**: V translations_cs-CZ.json jsou technické kódy (SQ015, SQ016) místo názvů lokalit
- **Příklad**: `"G3Q00002[SQ015]": "Co Vám v této lokalitě chybí? - SQ015"` 
- **Očekávané**: `"G3Q00002[SQ015]": "Co chybí v lokalitě Staré Modřany?"`
- **Lokace**: `src/translation_manager.py`, funkce `generate_translation_template()`

#### 5. **CHYBA: Chybějící propojení LSS struktury s CSV daty**
- **Problém**: Parser nerozpoznává, že různé question_codes v CSV odpovídají stejným otázkám v LSS
- **Příklad**: 
  - LSS: `"title": "G3Q00001"` (Staré Modřany)
  - CSV: `r306q0[SQ001]` (Nové Modřany)
  - CSV: `r664q0[SQ001]` (Beránek)
- **Lokace**: `src/data_transformer.py`, funkce `generate_question_mapping()`

### Dopad na workflow:
1. **Translations**: Chybí správné názvy otázek s lokalitami
2. **Chart generation**: Grafy nemají správné názvy
3. **Data processing**: Nesprávné mapování dat z CSV
4. **User experience**: Uživatel nevidí, ke které lokalitě se otázka vztahuje

### Potřebné opravy:
1. Upravit parser LSS pro rozpoznání opakujících se otázek
2. Vytvořit správné mapování mezi LSS a CSV kódy
3. Opravit generování překladů s názvy lokalit
4. Implementovat diagnostické nástroje pro kontrolu struktury

### Priorita: **KRITICKÁ** - blokuje správné zpracování dat a generování grafů

## Diagnostika potvrdila hlavní problémy:

### ✅ **POTVRZENO: Struktura LSS je správná**
- 8 skupin lokalit s jedinečnými kódy otázek
- Staré Modřany: G3Q00001, G3Q00002, G3Q00003, G3Q00004
- Nové Modřany: r306q0, r744q0, r576q0, r1001q0
- Beránek: r664q0, r270q0, r171q0, r702q0
- atd.

### ✅ **POTVRZENO: CSV obsahuje správné sloupce**
- 471 sloupců celkem
- Všechny kódy otázek z LSS jsou v CSV
- Např: G3Q00001[SQ001], r306q0[SQ001], r664q0[SQ001], atd.

### ❌ **HLAVNÍ PROBLÉM: Nesprávné mapování v parseru**
1. **Parser nerozpoznává správné skupiny pro otázky**
   - Všechny otázky jsou mapovány na "1: Staré Modřany"
   - Měly by být mapovány podle skutečné skupiny

2. **Chybí logika pro propojení kódů otázek se skupinami**
   - r306q0 → skupina "2: Nové Modřany"
   - r664q0 → skupina "3: Beránek"
   - atd.

### 🔧 **POTŘEBNÉ OPRAVY PARSERU:**
1. Opravit `generate_question_mapping()` v `data_transformer.py`
2. Opravit `_parse_hierarchical_questions()` v `lss_parser.py`
3. Implementovat správné mapování kódů otázek na skupiny lokalit
4. Zajistit, aby translations.json obsahoval správné názvy s lokalitami

### 📊 **STATISTIKY Z DIAGNOSTIKY:**
- LSS otázky: 77 nalezeno
- CSV sloupce: 471 nalezeno
- Společné otázky: pouze 30 (mělo by být více)
- Mapovací soubor: 118 kódů (nesprávně namapovaných)
