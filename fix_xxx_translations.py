#!/usr/bin/env python3
"""
Skript pro nahrazení XXX v translations_cs-CZ.json skutečnými názvy skupin
OPRAVA: Používá structure.json místo question_mapping.csv
"""

import json
import re

def load_question_to_group_mapping():
    """Načte mapování otázek a skupin ze structure.json"""
    structure_path = "data/dotazniky.urad.online/548754/structure.json"

    with open(structure_path, 'r', encoding='utf-8') as f:
        structure = json.load(f)

    # Vytvoříme mapování question_code -> group_name
    question_to_group = {}

    for group in structure.get('groups', []):
        group_name = group.get('group_name', '')
        gid = group.get('gid')

        # Vyčistíme název skupiny (odstraníme číslo a dvojtečku)
        if ':' in group_name:
            clean_group_name = group_name.split(':', 1)[1].strip()
        else:
            clean_group_name = group_name

        # Projdeme všechny otázky v této skupině
        for question in group.get('questions', []):
            question_code = question.get('title', '')
            if question_code:
                question_to_group[question_code] = clean_group_name

                # Přidáme i subquestions pokud existují
                subquestions = question.get('subquestions', {})
                if isinstance(subquestions, dict):
                    for subq_code, subq_data in subquestions.items():
                        full_subq_code = f"{question_code}[{subq_code}]"
                        question_to_group[full_subq_code] = clean_group_name

    return question_to_group

def fix_xxx_in_translations():
    """Nahradí XXX v translations_cs-CZ.json skutečnými názvy skupin"""

    # Načteme mapování
    question_to_group = load_question_to_group_mapping()

    # Načteme translations
    translations_path = "data/dotazniky.urad.online/548754/translations_cs-CZ.json"
    with open(translations_path, 'r', encoding='utf-8') as f:
        translations = json.load(f)

    # Projdeme question_names a nahradíme XXX (pouze pokud obsahuje XXX)
    changes_made = 0

    for question_code, question_text in translations.get('question_names', {}).items():
        # OPRAVA: Nahrazujeme pouze pokud text obsahuje XXX a není prázdný
        if question_text and 'XXX' in question_text:
            # Najdeme skupinu pro tuto otázku
            group_name = question_to_group.get(question_code)

            if group_name:
                # Nahradíme XXX názvem skupiny
                new_text = question_text.replace('XXX', group_name)
                translations['question_names'][question_code] = new_text
                changes_made += 1

                print(f"✅ {question_code}: '{question_text}' → '{new_text}'")
            else:
                print(f"❌ {question_code}: Skupina nenalezena pro '{question_text}'")

    # Uložíme opravené translations
    if changes_made > 0:
        with open(translations_path, 'w', encoding='utf-8') as f:
            json.dump(translations, f, ensure_ascii=False, indent=2)

        print(f"\n🎉 Úspěšně nahrazeno {changes_made} výskytů XXX!")
        print(f"📁 Soubor uložen: {translations_path}")
    else:
        print("\nℹ️  Žádné XXX k nahrazení nenalezeno")

if __name__ == "__main__":
    print("🔄 Nahrazuji XXX v translations_cs-CZ.json...")
    fix_xxx_in_translations()
    print("✅ Hotovo!")
